package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReasonReceivingRequest {
    private String barcode;
    private String comment;
    private String createdAt;
    private Integer gatepassItemId;
    private Integer id;
    private String primaryReason;
    private String secondaryReason;
    private String source;
    private Integer uwItemId;
}