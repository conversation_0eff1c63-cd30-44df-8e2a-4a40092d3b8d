package com.lenskart.cs.model;

import com.lenskart.commons.model.*;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class CsOrderContext {
        private String productIDToBeReturned;
        private String gatepassNo;
        private String cancellationType;
        private String cancelledBy;
        private String cancellationSource;
        private String paymentMethod;
        private String cancellationReason;
        private Integer cancellationReasonID;
        private String productIDToBeCancelled;
        private String cancelledOrderShipmentStatus;
        private Integer uwItemId;
        private String gatepassItemId;
        private String barcode;
        private String source;
        private String comment;
        private String createdAt;
        private Integer id;
        private String primaryReason;
        private String secondaryReason;


    @Builder
    @Data
    public static class ShippingEstimate {
        private Integer pincode;
        private ProductTypes classification;
        private FrameTypes frame_type;
        private PowerTypes powerTypes;
        private Boolean in_days;
        private Boolean dispach_date_req;
        private String packages;
        private String brand;
        private String lens_type;
        private String platform;
        private String r_sph;
        private String l_sph;
        private String r_cyl;
        private String l_cyl;
        private String r_axi;
        private String l_axi;
        private Boolean last_piece;
        private Countries lk_country;
        private Countries shipping_country;
        private Integer cart_item_id;
        private Boolean localFittingRequired;
        private String source_facility;
        private Boolean ship_to_store;
        private Boolean lens_only;
        private Countries source_country;
        private String coating_id;
        private String store_facility;
        private Boolean true_last_piece;
        private Boolean use_new;
        private String screen_name;
        private Integer dispatchDays;
        private String sipping_facility_source;
        private String promiseType;
    }

    @Builder
    @Data
    public static class ReceivingGatePass {
      private String productIDNeedsToBeReceived;
      private Integer gatepassItemId;
      private String userName;
      private String trackingNo;
      private String returnType;
      private String gatepassId;
    }
}