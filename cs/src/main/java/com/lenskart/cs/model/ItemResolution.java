package com.lenskart.cs.model;


import com.lenskart.core.model.ItemDetails;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ItemResolution {

    private Boolean alreadyInRetry;
    private String awbNumber;
    private String b2bFacilityCode;
    private String country;
    private String facility;
    private String gatePassCode;
    private Boolean gatePassSbrtEnabledStore;
    private String identifierId;
    private  String identifierType;
    private ItemDetails itemDetails;
    private Integer originalIncrementId;
    private Integer originalUwItemId;
    private Boolean postSbrtOrder;
    private Boolean productBadRecall;
    private Boolean psuedoGatepass;
    private QcFailParams  qcFailParams ;
    private Integer retryCount;
    private String returnType;
    private Boolean rtoItems;
    private String shipmentId;
    private String userEmail;
    private String userId;

    @Builder
    @Data
    public static class QcFailParams {
        private String primaryReason;
        private String secondaryReason;
        private List<String> imageList;
    }
}
