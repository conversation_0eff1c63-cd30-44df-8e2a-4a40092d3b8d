package com.lenskart.cs.model;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ItemDetailsProduct {

    private String colorName;
    private ItemDetailsProductColorOptions colorOptions;
    private List<String> productImage;
    private Integer productId;
    private Integer classification;
    private String productUrl;
    private String productName;
    private String splOrderFlag;
    private String fulfillable;
    private String isMultiple;
    private String colorCode;
    private Integer subProductId;
}
