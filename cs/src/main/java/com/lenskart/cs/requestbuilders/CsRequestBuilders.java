package com.lenskart.cs.requestbuilders;

import com.lenskart.commons.model.OrderContext;
import com.lenskart.core.model.ItemDetails;
import com.lenskart.cs.model.*;
import com.lenskart.cs.model.ItemSearchResponse;
import com.lenskart.cs.model.RTOItemReceivingRequest;
import com.lenskart.cs.model.ReturnOrderRequestDTO;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class CsRequestBuilders {

    public static RTOItemReceivingRequest getRTOItemReceivingRequest(OrderContext orderContext, ItemSearchResponse itemSearchResponse,CsOrderContext csOrderContext) {
        return RTOItemReceivingRequest.builder()
                .facility(getProduct(orderContext,csOrderContext.getProductIDToBeReturned()).getFacilityCode())
                .identifierId("shippingId")
                .identifierType(getProduct(orderContext,csOrderContext.getProductIDToBeReturned()).getShippingPackageId())
                .itemDetails(itemSearchResponse.getItemDetailsList())
                .returnType("shippingId")
                .shipmentId(getProduct(orderContext,csOrderContext.getProductIDToBeReturned()).getShippingPackageId())
                .userId("17722")
                .build();
    }

    public static OrderContext.ProductList getProduct(OrderContext orderContext, String productID){
        List<OrderContext.ProductList> productList=orderContext.getProductLists();
        for (OrderContext.ProductList product : productList) {
            if (productID.equals(product.getProductId())) {
                return product;
            }
        }
        return null;
    }

    private static ReturnPickupAddress getReturnPickUpAddress(){

        return ReturnPickupAddress.builder()
                .city("NEW DELHI")
                .email("<EMAIL>")
                .state("DELHI")
                .country("India")
                .pincode(110059)
                .first_name("MEENU")
                .last_name("MEENU")
                .street_1("A1/157,2ND FLOOR,FRONT SIDE")
                .telephone("+91-**********")
                .street_2("UTTAM NAGAR,NEW DELHI").build();
    }


    private static List<ReturnItemReasonDetails> getReturnReasonDetailsList() {
        ReturnItemReasonDetails reasonDetails = ReturnItemReasonDetails.builder()
                .primary_reason_id(2003)
                .type("RETURN")
                .additional_comments("Return")
                .secondary_reason_id(211)
                .build();
        return Collections.singletonList(reasonDetails);
    }

    private static List<ReturnItemDetails> getReturnItemDetails(OrderContext orderContext,CsOrderContext csOrderContext){
        ReturnItemDetails returnItemDetails = ReturnItemDetails.builder()
                .reasons(getReturnReasonDetailsList())
                .magento_item_id(getProduct(orderContext,csOrderContext.getProductIDToBeReturned()).getItemId())
                .uw_item_id(null)
                .claim_insurance(false)
                .do_refund(true)
                .need_approval(false)
                .qc_status("Pass")
                .refund_method("storecredit")
                .refund_method_request("storecredit")
                .build();
        return Collections.singletonList(returnItemDetails);
    }

    public static ReturnRequest getReturnRequestDetails(OrderContext orderContext,CsOrderContext csOrderContext){
        return ReturnRequest.builder()
                .items(getReturnItemDetails(orderContext,csOrderContext))
                .pickup_address(getReturnPickUpAddress())
                .storeEmail(null)
                .is_courier_reassigned(null)
                .store_facility_code(null)
                .salesman_number("**********")
                .salesman_name("Ankit Singh")
                .return_method("storecredit")
                .return_source("vsm")
                .incrementId(null)
                .newCourier(null)
                .oldCourier(null)
                .initiated_by(null)
                .facility_code(null)
                .entity(null)
                .build();

    }


    public static ReturnIReversePickInfoRequest getReturnReversePickInfoRequest(ReturnResponse returnResponse) {
        return ReturnIReversePickInfoRequest.builder()
                .awb(returnResponse.getReversePickUpDetails().getAwbNumber())
                .courier(returnResponse.getReversePickUpDetails().getCourier())
                .pickupId(returnResponse.getResult().getGroup_id())
                .referenceId(String.valueOf(returnResponse.getReversePickUpDetails().getIncrementId())+returnResponse.getResult().getGroup_id())
                .userId(0)
                .userName("Ankit")
                .build();
    }

    public static String createPayloadForRefund(int orderId) {
        JSONObject payload = new JSONObject();
        payload.put("exchangeOrderId", 0);
        payload.put("masterOrderId", orderId);
        return payload.toString();
    }


    public static ReturnOrderRequestDTO createPayloadForReturn(OrderContext orderContext,CsOrderContext csOrderContext,ReturnResponse returnResponse) {

        ReturnOrderRequestDTO returnOrderRequestDTO = ReturnOrderRequestDTO.builder()
                .incrementId(orderContext.getOrderId())
                .source("web")
                .referenceOrderCode(String.valueOf(orderContext.getUnicomOrderCode()))
                .doRefund(true)
                .isDualCo(false)
                .facility(getProduct(orderContext,csOrderContext.getProductIDToBeReturned()).getFacilityCode())
                .raiseRPUatUnicom(true)
                .raiseRPUatNexs(false)
                .rtoItem(false)
                .awaitedRtoItem(false)
                .groupId(returnResponse.getResult().getGroup_id())
                .reasonDetail("Customer Request")
                .refundMethod("Credit Card")
                .shippingPackageId(getProduct(orderContext,csOrderContext.getProductIDToBeReturned()).getShippingPackageId())
                .uwOrderDTOs(List.of())
                .orderDTOs(List.of())
                .ordersHeaderDTO(null)
                .userId("user123")
                .build();
        return returnOrderRequestDTO;


    }

    public static CancellationRequest getCancellationRequest(CsOrderContext csOrderContext) {
        return CancellationRequest.builder()
                .initiated_by(csOrderContext.getCancelledBy())
                .source(csOrderContext.getCancellationSource())
                .payment_method(csOrderContext.getPaymentMethod())
                .reason_detail(csOrderContext.getCancellationReason())
                .reason_id(csOrderContext.getCancellationReasonID())
                .cancellation_type(csOrderContext.getCancellationType())
                .build();
    }

    public static PinCodeEligibility getPincodeEligibility(CsOrderContext.ShippingEstimate csOrderContextShippingEstimate) {
        return PinCodeEligibility.builder()
                .pincode(String.valueOf(csOrderContextShippingEstimate.getPincode()))
                .source(csOrderContextShippingEstimate.getSipping_facility_source())
                .promise_type(csOrderContextShippingEstimate.getPromiseType())
                .country_code(csOrderContextShippingEstimate.getShipping_country())
                .facility_code(csOrderContextShippingEstimate.getSource_facility())
                .build();
    }

    public static DeliveryETAModel getDeliveryEtaModel(CsOrderContext.ShippingEstimate csOrderContextShippingEstimate) {
        return DeliveryETAModel.builder()
                .pincodeModel(new DeliveryETAModel.PincodeModel(csOrderContextShippingEstimate.getPincode()))
                .dispatchDays(csOrderContextShippingEstimate.getDispatchDays())
                .platformModel(new DeliveryETAModel.PlatformModel(csOrderContextShippingEstimate.getPlatform()))
                .sourceFacility(csOrderContextShippingEstimate.getSource_facility())
                .destinationCountry(csOrderContextShippingEstimate.getShipping_country())
                .shipToStore(csOrderContextShippingEstimate.getShip_to_store())
                .build();
    }

    public static LocalFittingGatepassRequest localFittingGatepassRequest(CsOrderContext.ReceivingGatePass csOrderContextReceivingGatePass,OrderContext orderContext) {
        return LocalFittingGatepassRequest.builder()
                .gatepassNo(csOrderContextReceivingGatePass.getGatepassNo())
                .incrementId(orderContext.getOrderId())
                .gatepassItemId(csOrderContextReceivingGatePass.getGatepassItemId())
                .uwItemId(csOrderContextReceivingGatePass.getUwItemId())
                .facilityCode(getProduct(orderContext, csOrderContextReceivingGatePass.getProductIDNeedsToBeReceived()).getFacilityCode())
                .build();
    }
    public static ItemResolution itemResolutionModel(OrderContext orderContext,CsOrderContext csOrderContext){
        return ItemResolution.builder()
        .alreadyInRetry(true)
        .awbNumber("")
        .b2bFacilityCode("")
        .country("IN")
        .facility(getProduct(orderContext,csOrderContext.getProductIDToBeReturned()).getFacilityCode())
        .gatePassCode("")
        .gatePassSbrtEnabledStore(true)
        .identifierId("")
        .identifierType("")
        .itemDetails(new ItemDetails())
        .originalIncrementId(0)
        .originalUwItemId(0)
        .postSbrtOrder(true)
        .productBadRecall(true)
        .psuedoGatepass(true)
        .qcFailParams(ItemResolution.QcFailParams.builder().primaryReason("").secondaryReason("").imageList(new ArrayList<>()).build())
        .retryCount(0)
        .returnType("")
        .rtoItems(true)
        .shipmentId("")
        .userEmail("")
        .userId("")
        .build();
    }
}
