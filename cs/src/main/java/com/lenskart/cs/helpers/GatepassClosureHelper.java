package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.GatePassClosureResponse;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.cs.endpoints.CsEndpoints.GATE_PASS_CLOSURE;


@SuperBuilder
public class GatepassClosureHelper extends CsBaseHelper implements ServiceHelper {


    OrderContext orderContext;
    Response response;
    CsOrderContext csOrderContext;
    GatePassClosureResponse gatePassClosureResponse;

    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext,csOrderContext);
        queryParams = getPassClosureSearchQueryParams(csOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(GATE_PASS_CLOSURE.getUrl(),
                headers,
                queryParams,
                statusCode
        );
        gatePassClosureResponse = response.as(GatePassClosureResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
