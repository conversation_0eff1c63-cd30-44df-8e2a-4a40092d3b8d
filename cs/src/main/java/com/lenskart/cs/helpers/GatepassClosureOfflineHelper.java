package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.cs.endpoints.CsEndpoints.DELIVERY_ETA;
import static com.lenskart.cs.endpoints.CsEndpoints.GATE_PASS_CLOSUREOFFLINE;


@SuperBuilder
public class GatepassClosureOfflineHelper extends CsBaseHelper implements ServiceHelper {


    String payload;
    OrderContext orderContext;
    Response response;
    CsOrderContext.ReceivingGatePass csOrderContextReceivingGatePass;


    @Override
    public ServiceHelper init() {
        payload = JsonUtils.convertObjectToJsonString(CsRequestBuilders.gatepassClosureofflineModel(csOrderContextReceivingGatePass));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(GATE_PASS_CLOSUREOFFLINE.getUrl(), null, payload,200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
