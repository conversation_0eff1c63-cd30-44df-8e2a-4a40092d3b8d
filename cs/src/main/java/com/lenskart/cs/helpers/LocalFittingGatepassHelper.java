package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import static com.lenskart.cs.endpoints.CsEndpoints.LOCAL_FITTING_GATE_PASS_RECEIVING;

@SuperBuilder
public class LocalFittingGatepassHelper extends CsBaseHelper implements ServiceHelper {
    String payload;
    OrderContext orderContext;
    Response response;
    CsOrderContext csOrderContext;
    CsOrderContext.ReceivingGatePass csOrderContextReceivingGatePass;

    @Override
    public ServiceHelper init() {
        payload = JsonUtils.convertObjectToJsonString(CsRequestBuilders.localFittingGatepassRequest(csOrderContextReceivingGatePass,orderContext,csOrderContext));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(LOCAL_FITTING_GATE_PASS_RECEIVING.getUrl(),headers,payload,200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
