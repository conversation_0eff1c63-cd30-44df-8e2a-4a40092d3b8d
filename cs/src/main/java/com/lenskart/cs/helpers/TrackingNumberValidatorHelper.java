package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import io.restassured.response.Response;
import lombok.Builder;
import lombok.experimental.SuperBuilder;

import java.util.Map;

import static com.lenskart.cs.endpoints.CsEndpoints.TRACKING_NUMBER_VALIDATOR;

@SuperBuilder
public class TrackingNumberValidatorHelper extends CsBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    CsOrderContext csOrderContext;
    CsOrderContext.ReceivingGatePass csOrderContextReceivingGatePass;

    @Override
    public ServiceHelper init() {
        headers = getTrackingValidatorHeaders(orderContext,csOrderContext,csOrderContextReceivingGatePass);
        return this;
    }

    @Override
    public ServiceHelper process() {

        response = RestUtils.get(TRACKING_NUMBER_VALIDATOR.getUrl(Map.of("id", String.valueOf(csOrderContext.getGatepassNo()),
                "trackingNumber",csOrderContextReceivingGatePass.getTrackingNo(),
                "userId", csOrderContextReceivingGatePass.getUserName(),
                "returnType", csOrderContextReceivingGatePass.getReturnType(),
                "facilityCode", CsRequestBuilders.getProduct(orderContext,csOrderContext.getProductIDToBeReturned()).getFacilityCode())),
                headers,
                queryParams,
                200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
