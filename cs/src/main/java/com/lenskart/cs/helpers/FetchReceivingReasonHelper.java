package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.FetchReceivingReasonResponse;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.cs.endpoints.CsEndpoints.FETCH_RECEIVING_REASON;


@SuperBuilder
public class FetchReceivingReasonHelper extends CsBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    CsOrderContext csOrderContext;
    FetchReceivingReasonResponse fetchReceivingReasonResponse;

    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext,csOrderContext);
        queryParams = receivingReasonQueryParams(csOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(FETCH_RECEIVING_REASON.getUrl(),
                headers,
                queryParams,
                statusCode
        );
        fetchReceivingReasonResponse = response.as(FetchReceivingReasonResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}
