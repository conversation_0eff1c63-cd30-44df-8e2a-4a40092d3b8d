package com.lenskart.nexs.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.SneakyThrows;

import java.sql.SQLException;

public class SensieUtils {
    @SneakyThrows
    public static String getLatestDoOrderId(NexsOrderContext nexsOrderContext)  {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.ORDER_SENSIE_DB,
                SensieQueries.DO_ORDER_ID).getFirst().get("id").toString();
    }
}
