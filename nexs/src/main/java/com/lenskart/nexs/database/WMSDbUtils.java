package com.lenskart.nexs.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;

import java.util.List;
import java.util.Map;

public class WMSDbUtils {

    public static String getStatusOfShipment(NexsOrderContext nexsOrderContext) {
        List<Map<String, Object>> itemTypeForStatus = MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.WMS_DB,
                        WMSQueries.ITEM_TYPE_FOR_STATUS,
                        nexsOrderContext.getShippingId());

        List<Map<String, Object>> itemStatus = MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.WMS_DB,
                        WMSQueries.ITEM_STATUS,
                        itemTypeForStatus.getFirst().get("item_type"), nexsOrderContext.getNexsOrderId(),
                        nexsOrderContext.getShippingId());

        return itemStatus.getFirst().get("status").toString();
    }

    public static String getStausBasedOnBarcode(NexsOrderContext nexsOrderContext, String barcode) {
        List<Map<String, Object>> leftLensStatus = MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.WMS_DB,
                        WMSQueries.GET_ORDER_ITEM_STATUS,
                        barcode, nexsOrderContext.getTrayBarcode());
        return leftLensStatus.getFirst().get("status").toString();
    }

    public static List<Map<String, Object>> getMeiSyncDetails(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.WMS_DB,
                        WMSQueries.MEI_SYNC_DETAILS, nexsOrderContext.getNexsOrderId(), nexsOrderContext.getFittingId());
    }

    public static List<Map<String, Object>> getFittingStatus(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.WMS_DB,
                        WMSQueries.FITTING_STATUS, nexsOrderContext.getFittingId());
    }

    public static String getFacilityCode(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB,
                WMSQueries.GET_FACILITY_CODE,
                nexsOrderContext.getShippingId()).getFirst().get("facility_code").toString();
    }

    public static List<Map<String, Object>> getShipmentStatus(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB,
                WMSQueries.DISTINCT_STATUS,
                nexsOrderContext.getShippingId());
    }

    public static List<Map<String, Object>> itemStatusByShippingId(String shippingId) {
       return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB,
                WMSQueries.ITEM_STATUS_BY_SHIPPING_ID, shippingId
        );
    }
    public static String getReassignStatus(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB,
                WMSQueries.REASSIGNMENT_STATUS_BY_SHIPPING_ID,
                nexsOrderContext.getShippingId()).getFirst().get("status").toString();
    }

}
