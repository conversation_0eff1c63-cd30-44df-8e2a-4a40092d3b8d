package com.lenskart.nexs.config;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;


/**
 * Configuration class for the Nexs module
 */
@Data
@NoArgsConstructor
public class NexsConfig {

    // Environment configurations
    private Map<String, EnvironmentConfig> environments = new HashMap<>();

    /**
     * Gets the configuration for a specific environment
     *
     * @param environment Environment name (e.g., "preprod", "prod")
     * @return Environment configuration
     */
    public EnvironmentConfig getEnvironment(String environment) {
        return environments.get(environment);
    }

    /**
     * Configuration for a specific environment
     */
    @Data
    @NoArgsConstructor
    public static class EnvironmentConfig {
        // Base URLs for different services
        private Map<String, String> baseUrls = new HashMap<>();
        
        // Credentials by serial number
        private Map<String, SerialNoConfig> credentials = new HashMap<>();

        /**
         * Gets the base URL for a specific service
         *
         * @param serviceName Service name
         * @return Base URL for the service
         */
        public String getBaseUrl(String serviceName) {
            return baseUrls.get(serviceName);
        }
        
        /**
         * Gets the credentials for a specific serial number
         *
         * @param serialNumber Serial number
         * @return Credentials for the serial number
         */
        public SerialNoConfig getSerialNoConfig(String serialNumber) {
            return credentials.get(serialNumber);
        }
    }

    /**
     * Configuration for credentials by serial number
     */
    @Data
    @NoArgsConstructor
    public static class SerialNoConfig {
        private Object username;
        private Object password;
    }
}
