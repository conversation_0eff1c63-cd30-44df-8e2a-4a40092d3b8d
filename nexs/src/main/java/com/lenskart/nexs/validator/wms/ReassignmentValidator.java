package com.lenskart.nexs.validator.wms;

import com.lenskart.commons.base.IValidator;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.helpers.wms.ReassignementHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;
import org.testng.Assert;
@SuperBuilder
public class ReassignmentValidator implements IValidator {
    NexsOrderContext nexsOrderContext;
    ReassignementHelper reassignementHelper;

    @Override
    public void validateNode() {
        if(nexsOrderContext.getIsValidationRequired()){
            Assert.assertEquals(reassignementHelper.getResponse().getStatusCode(), 200);
            validateDBEntities();
        }
    }

    @Override
    public void validateDBEntities() {
        if(nexsOrderContext.getIsValidationRequired()){
            String status = WMSDbUtils.getReassignStatus(nexsOrderContext);
            Assert.assertTrue(
                    status.equals("REASSIGNMENT_PENDING") || status.equals("REASSIGNMENT_DONE"),
                    "Actual value '" + status + "' did not match any expected facility"
            );
        }
    }
}
