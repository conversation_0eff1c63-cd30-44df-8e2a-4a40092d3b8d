package com.lenskart.nexs.helpers.jit;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.model.NexsOrderContext.Headers;
import com.lenskart.nexs.model.jit.RxuStatusModel;
import com.lenskart.nexs.requestBuilder.jit.JitRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.JIT_RXUSTATUS;

@SuperBuilder
public class RxStatusHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    Response response;
    private JSONObject payload;

    @Override
    public ServiceHelper init() {
        payload = JitRequestBuilder.RxuStatusRequestBuilder(nexsOrderContext, nexsOrderContext.getFittingId(), nexsOrderContext.getStationId());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(JIT_RXUSTATUS.getUrl(), null, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {

        init();
        process();
        validate();
        return this;
    }
}
