package com.lenskart.nexs.helpers.picking;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.PickingRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.PICKING_CREATE_PICKING_SUMMARY_DO;
import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.PICKING_DO_CLOSE_PICKING;

@SuperBuilder
@Slf4j
public class DoClosePickingHelper extends NexsBaseHelper implements ServiceHelper {
    String payload;
    NexsOrderContext nexsOrderContext;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        payload = PickingRequestBuilder.DoClosePickingPayload(nexsOrderContext.getShippingId());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(PICKING_DO_CLOSE_PICKING.getUrl(), headers, payload, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        return init().process().validate();
    }

}

