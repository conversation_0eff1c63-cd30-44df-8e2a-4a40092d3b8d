package com.lenskart.nexs.helpers.sensei;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.database.SensieUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.lenskart.commons.utils.RestUtils.getValueFromResponse;
import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.ORDER_SENSEI_GET_ORDER_DETAILS;

@SuperBuilder
@Slf4j
public class CreateDOOrderHelper extends NexsBaseHelper implements ServiceHelper {

    Response response;
    String lastestDoOrderId;
    NexsOrderContext nexsOrderContext;

    @SneakyThrows
    @Override
    public ServiceHelper init() {
        lastestDoOrderId = SensieUtils.getLatestDoOrderId(nexsOrderContext);
        lastestDoOrderId=String.valueOf(Integer.parseInt(lastestDoOrderId)+1);
        headers = getHeaders(nexsOrderContext);
        return this;

    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(ORDER_SENSEI_GET_ORDER_DETAILS.getUrl(Map.of("doOrderId",lastestDoOrderId )), headers, null, 200);
        log.info("DO order created api response: {}", getValueFromResponse(response,"meta.displayMessage"));
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}
