package com.lenskart.nexs.helpers.ims;

import com.google.protobuf.Service;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.NexsBaseHelper;

import lombok.experimental.SuperBuilder;

import java.util.List;

@SuperBuilder
public class AddGAAInventoryHelper extends NexsBaseHelper implements ServiceHelper {
    String location;
    String facility;
    int pid;
    String legalOwner;
    String barcode;
    List<String> operations;
    @Override
    public ServiceHelper init() {
        operations = List.of(Constants.GRN_QC_PASS, Constants.PUTAWAY_PENDING, Constants.PUTAWAY_COMPLETE);
         barcode = GenericUtils.genrateRandomNumericString(4) +
                GenericUtils.genrateRandomAlphabeticString(4).toUpperCase();
        return this;
    }

    @Override
    public ServiceHelper process() {

        for (String operation : operations) {
            String resolvedLocation = switch (operation) {
                case Constants.GRN_QC_PASS -> "GRN";
                case Constants.PUTAWAY_PENDING -> "";
                case Constants.PUTAWAY_COMPLETE -> location;
                default -> throw new IllegalArgumentException("Unsupported IMS operation: " + operation);
            };

            StockInAndOutV2Helper.builder()
                    .barcode(barcode)
                    .facility(facility)
                    .location(resolvedLocation)
                    .operation(operation)
                    .pid(pid)
                    .legalOwner(legalOwner)
                    .updatedBy("Automation")
                    .build()
                    .test();

            AwaitUtils.sleepMillis(500);
        }

        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        return init().process().validate();
    }
}
