package com.lenskart.nexs.requestBuilder.jit;

import com.lenskart.nexs.model.NexsOrderContext;
import org.json.JSONArray;
import org.json.JSONObject;
import java.text.SimpleDateFormat;
import java.util.Date;

public class JitRequestBuilder {

    public static JSONObject RxuStatusRequestBuilder(NexsOrderContext nexsOrderContext, String jobId, String stationId) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String formattedDateTime = dateFormat.format(new Date());
        JSONObject rxuStatusItem = new JSONObject();
        rxuStatusItem.put("dateTime", formattedDateTime);
        rxuStatusItem.put("jobId", jobId);
        rxuStatusItem.put("stationDesc", "test");
        rxuStatusItem.put("stationId", stationId);

        JSONArray rxuStatusArray = new JSONArray();
        rxuStatusArray.put(rxuStatusItem);
        JSONObject request = new JSONObject();
        request.put("rxuStatus", rxuStatusArray);
        return request;
    }
}
