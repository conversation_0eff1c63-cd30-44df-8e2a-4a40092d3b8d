package com.lenskart.nexs.requestBuilder;

import org.json.JSONObject;

public class PickingRequestBuilder {

    public static String createPickingSummaryPayload(String shippingPackageID) {
        JSONObject payload = new JSONObject();
        payload.put("shippingPackageID", shippingPackageID);
        return payload.toString();
    }

    public static String DoClosePickingPayload(String shippingPackageID) {
        JSONObject payload = new JSONObject();
        payload.put("shippingPackageID", shippingPackageID);
        return payload.toString();

    }
}
