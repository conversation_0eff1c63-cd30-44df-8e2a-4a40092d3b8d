package com.lenskart.nexs.endpoints;

import com.lenskart.commons.endpoints.BaseEndpoint;
import lombok.Getter;

import java.util.Map;

@Getter
public enum NexsOrderProcessingEndpoints implements BaseEndpoint {

    //Picking Service Endpoints
    PICKING_GET_LOCATION_CATEGORIES("/nexs/order/picking/location/categories", "nexsService"),
    PICKING_SUMMARY("/nexs/order/picking/summary/{$location}/{$categories}/standard", "nexsService"),
    PICKING_CLOSE_PICKING_SUMMARY("/nexs/order/picking/complete/{$id}", "nexsService"),
    PICKING_DISPLAY_PICKLIST("/nexs/order/picking/{$pickingSummaryId}", "nexsService"),
    PICKING_SCAN_BARCODE("/nexs/order/picking/scan/{$picklistId}/{$barcode}", "nexsService"),
    PICKING_VIEW_ORDER("/nexs/order/picking/view/order", "nexsService"),
    PICKING_VALIDATE_FRAME("/nexs/order/picking/validate/frame", "nexsService"),
    PICKING_LENS_PICKING_DETAILS("/nexs/order/picking/details/{$fittingId}", "nexsService"),
    PICKING_SCAN_LENS("/nexs/order/picking/lens/scan", "nexsService"),
    PICKING_COMPLETE_LENS_PICKING("/nexs/order/picking/v2/lens/summary/{$pickingSummaryId}/complete", "nexsService"),
    PICKING_GET_WAVE_DETAILS("/nexs/order/picking/addverb/wave/{$waveId}", "nexsService"),
    PICKING_DISCARD_ASRS_ORDER("/nexs/order/picking/supervisor/discard/asrs/Order/{$shippingPackageId}", "nexsService"),
    PICKING_SYNC_ORDER_TO_ES("/nexs/order/picking/order/item/syncToES", "nexsService"),
    PICKING_DYNAMIC_PICKING("/nexs/order/picking/pick/orderItemId/{$orderItemId}/{$barcode}", "nexsService"),
    PICKING_ADDVERB_CREATE_WAVE("/nexs/order/picking/addverb/create/wave", "nexsService"),
    PICKING_ADDVERB_PICK_ITEM("/nexs/order/picking/addverb/pick/item", "nexsService"),
    PICKING_ADDVERB_COMPLETE("/nexs/order/picking/addverb/complete", "nexsService"),
    PICKING_CHANGE_ES_STATUS("/nexs/order/picking/supervisor/es/change/status", "nexsService"),
    PICKING_CREATE_PICKING_SUMMARY_DO("/nexs/order/picking/DO/createPickingSummary", "nexsService"),
    PICKING_PICK_BARCODE_DO("/nexs/order/picking/DO/pickBarcode", "nexsService"),
    PICKING_GET_PICKING_SUMMARY_DO("/nexs/order/picking/DO/getPickingSummary", "nexsService"),


    //Packing Service Endpoints
    PACKING_GET_ITEM_DETAIL_BY_ID("/nexs/packing/getItemDetailById", "nexsService"),
    PACKING_COMPLETE_PACKING("/nexs/packing/completePacking", "nexsService"),
    PACKING_PACKING_SLIP("/nexs/packing/label/pdf", "nexsService"),

    //OrderQC Service Endpoints
    ORDERQC_FETCH_ORDER_ENTITY_TYPE("/nexs/api/orderqc/fetch/order/entityType", "nexsService"),
    ORDERQC_COMPLETE_QC("/nexs/api/orderqc/completeQc", "nexsService"),

    //WMS Service Endpoints
    WMS_GET_COMMENT("/nexs/wms/api/v1/comment/get", "nexsService"),
    WMS_PRINT_SHIPMENT("/nexs/wms/api/v1/shipment/print", "nexsService"),
    WMS_PRINT_SHIPMENT_MP("/nexs/wms/api/v1/shipment/mporder/print", "nexsService"),
    WMS_CREATE_TRAY("/nexs/wms/api/v1/tray/create", "nexsService"),
    WMS_LOCATION_SCAN("/nexs/wms/api/v1/location/scan", "nexsService"),
    WMS_EXPORT_PDF("/nexs/wms/api/v1/exportPdf", "nexsService"),
    WMS_ADD_VSM_COMMENT("/nexs/wms/api/v1/vsmComment/add", "nexsService"),
    WMS_REASSIGN_SHIPMENT("/nexs/wms/api/v1/shipment/reassign", "nexsService"),
    WMS_ORDER_DETAILS("/nexs/wms/api/v1/order/details/header", "nexsService"),
    WMS_ORDER_DETAILS_WITH_ID("/nexs/wms/api/v1/order/details/id/{$shippingPackageId}", "nexsService"),
    WMS_HAND_EDGING_SCAN("/nexs/wms/api/v1/hand/edging/scan", "nexsService"),
    WMS_FETCH_INVOICE("/nexs/wms/api/v1/invoice/fetch", "nexsService"),
    WMS_ORDER_DETAILS_OVERVIEW("/nexs/wms/api/v1/order/details/overview", "nexsService"),
    WMS_FACILITY_REASSIGNMENT("/nexs/wms/api/v1/shipment/reassign", "nexsService"),

    //Fitting Service Endpoints
    FITTING_GET_FITTING_DETAIL("nexs/fitting/getFittingDetail/{$fittingId}", "nexsService"),
    FITTING_MARK_FITTING_COMPLETE("/nexs/fitting/markFittingComplete/{$fittingId}/{$shippingPackageId}", "nexsService"),

    //IMS Service Endpoints
    IMS_STOCK_IN_AND_OUT("/nexs/api/ims/stockInAndOut","nexsService"),
    IMS_FETCH_BARCODE_ITEM_DETAILS("/nexs/api/ims/fetchBarcodeItemDetails","nexsService"),
    IMS_FETCH_INVENTORY_INFO("/nexs/api/ims/fetchInventoryInfo","nexsService"),
    IMS_GET_CONSOLIDATED_INV_INFO("/nexs/api/ims/getConsolidatedInvInfo/{$productId}","nexsService"),
    IMS_STOCK_IN_AND_OUT_V2("/nexs/api/ims/stockInAndOutV2","nexsService"),
    IMS_UPDATE_BARCODE_STATUS("/nexs/api/ims/update/{$id}","nexsService"),
    IMS_UPDATE_BARCODE_DESTINATION("/nexs/api/ims/update/barcodeDestination","nexsService"),
    IMS_UPDATE_LOCATION("/nexs/api/ims/update/location","nexsService"),
    IMS_STOCK_IN_OUT_AND_RELEASE("/nexs/api/ims/stockInOutAndRelease","nexsService"),

    //LK Auth Service Endpoints
    LKAUTH_LOGIN("/v1/user/login", "nexsService"),
    LKAUTH_LOGOUT("/v1/user/logout", "nexsService"),
    LKAUTH_FORGOT_PASSWORD("/v1/forgot/password/{$email}", "nexsService"),

    //Manifest Service Endpoints
    MANIFEST_FETCH_CHANNEL("/nexs/manifest/api/v1/fetch/channel", "nexsService"),
    MANIFEST_FETCH_SHIPPING_PROVIDER("/nexs/manifest/api/v1/fetch/shippingProvider/{$channel}", "nexsService"),
    MANIFEST_SAVE("/nexs/manifest/api/v1/save", "nexsService"),
    MANIFEST_FETCH_MANIFEST("/nexs/manifest/api/v1/fetch/{$manifestNumber}", "nexsService"),
    MANIFEST_ADD_SHIPMENT("/nexs/manifest/api/v1/add/shipment/{$manifestNumber}/{$awbNumber}", "nexsService"),
    MANIFEST_CLOSE("/nexs/manifest/api/v1/close/{$manifestNumber}", "nexsService"),

    //Consolidation Service Endpoints
    CONSOLIDATION_SCAN_ITEM("/nexs-consolidation/api/v1/scan/item", "nexsService"),
    CONSOLIDATION_OPTIONS("/nexs-consolidation/api/v1/options", "nexsService"),
    CONSOLIDATION_ALLOCATE_ITEM("/nexs-consolidation/api/v1/allocate/item", "nexsService"),
    CONSOLIDATION_ORDER_FLUSH("/nexs-consolidation/packing/api/v1/order/flush", "nexsService"),
    CONSOLIDATION_SCAN_ITEM_IN_PACKING("/nexs-consolidation/packing/api/v1/scan/item", "nexsService");


    private final String endpoint;
    private final String serviceName;

    NexsOrderProcessingEndpoints(String endpoint, String serviceName) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
    }

    @Override
    public String getUrl() {
        return NexsEndpointManager.getEndpointUrl(this);
    }

    @Override
    public String getUrl(Map<String, String> pathParams) {
        return NexsEndpointManager.getEndpointUrl(this, pathParams);
    }

}
