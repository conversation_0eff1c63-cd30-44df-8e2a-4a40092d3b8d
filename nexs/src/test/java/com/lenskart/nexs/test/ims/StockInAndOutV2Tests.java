package com.lenskart.nexs.test.ims;

import com.lenskart.nexs.helpers.ims.AddGAAInventoryHelper;
import org.testng.annotations.Optional;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

public class StockInAndOutV2Tests {

    @Parameters({ "pid", "location", "facility", "legalOwner" })
    @Test(description = "Perform stock-in and stock-out transitions using different IMS operations")
    public void stockInAndOutV2(
            @Optional("38728") int pid,
            @Optional("test-1-1") String location,
            @Optional("PBR01") String facility,
            @Optional("LKIN") String legalOwner
    ) {
        AddGAAInventoryHelper.builder()
                .pid(pid)
                .location(location)
                .facility(facility)
                .legalOwner(legalOwner)
                .build()
                .test();
        }
    }

