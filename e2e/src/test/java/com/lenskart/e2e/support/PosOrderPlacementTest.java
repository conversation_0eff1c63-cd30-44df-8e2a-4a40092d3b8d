package com.lenskart.e2e.support;

import com.lenskart.commons.model.*;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.helpers.AuthenticationHelper;
import com.lenskart.pos.helpers.GetSalesmanListHelper;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import com.lenskart.pos.model.POS;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;


import java.util.List;

import static com.lenskart.commons.model.PowerTypes.ZERO_POWER;

@Slf4j
public class PosOrderPlacementTest {

    @Test
    public void posOrderPlacement() {

        OrderContext orderContext = OrderContext.builder()
                .phoneNumber("2035164014")
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId("148248")
                                .powerType(ZERO_POWER)
                                .itemType(ItemType.DTC)
                                .isPrescriptionRequired(false)
                                .build()))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("560075")
                        .build())
                .isPosOrder(true)
                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                        .country(Countries.IN)
                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                        .build())
                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                .build();


        PosOrderCreationHelper posOrderCreationHelper = PosOrderCreationHelper
                .builder()
                .orderContext(orderContext)
                .build();
        posOrderCreationHelper.test();

        log.info("Order ID - {}", orderContext.getOrderId());
    }

    @Test
    public void GetSalesmanList() {
        OrderContext orderContext = OrderContext.builder()
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .build())
                .isPosOrder(true)
                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                        .country(Countries.IN)
                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                        .franchiseId(106)
                        .build())
                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                .build();

        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        GetSalesmanListHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }
}