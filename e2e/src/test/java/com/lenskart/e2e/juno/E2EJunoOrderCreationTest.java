package com.lenskart.e2e.juno;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.JunoE2EDataProvider;
import com.lenskart.e2e.helper.juno.JunoE2EHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

@Slf4j
@TestCategory(TestCategory.Category.E2E)
public class E2EJunoOrderCreationTest {

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeGlassSingeVisionPower")
    public void eyeGlassSingeVisionPowerOrderCreation(OrderContext orderContext) {

        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeGlassBifocalPower")
    public void eyeGlassBifocalPowerOrderCreation(OrderContext orderContext) {

        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeGlassZeroPower")
    public void eyeGlassZeroPowerOrderCreation(OrderContext orderContext) {

        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }


    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithoutPower")
    public void eyeglassWithoutPowerOrderCreation(OrderContext orderContext) {

        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }


    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithoutPower")
    public void sunglassWithoutPowerOrderCreation(OrderContext orderContext) {

        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPower")
    public void sunglassWithPowerOrderCreation(OrderContext orderContext) {

        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }


    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "goldPid")
    public void goldPidOrderCreation(OrderContext orderContext) {

        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }


    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "frameOnly")
    public void frameOnlyOrderCreation(OrderContext orderContext) {

        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPower")
    public void sunglassWithPower(OrderContext orderContext) {

        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }


    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "contactLens")
    public void contactLensOrderCreation(OrderContext orderContext) {
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "contactLensZeroPower")
    public void contactLensZeroPowerOrderCreation(OrderContext orderContext) {
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "accessories")
    public void accessoriesOrderCreation(OrderContext orderContext) {
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassesZeroPowerWithSunglassesAndContactLens")
    public void eyeglassesZeroPowerWithSunglassesAndContactLensOrderCreation(OrderContext orderContext) {
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassesWithPowerAndSunglassesAndContactLens")
    public void eyeglassesWithPowerAndSunglassesAndContactLensOrderCreation(OrderContext orderContext) {
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

}

