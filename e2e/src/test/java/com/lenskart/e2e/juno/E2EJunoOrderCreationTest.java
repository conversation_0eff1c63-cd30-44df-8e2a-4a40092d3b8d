package com.lenskart.e2e.juno;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.JunoE2EDataProvider;
import com.lenskart.e2e.helper.juno.JunoE2EHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;

@Slf4j
@TestCategory(TestCategory.Category.SANITY)
public class E2EJunoOrderCreationTest {


    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "orderContext")
    public void createJunoOrder(OrderContext orderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }
}
