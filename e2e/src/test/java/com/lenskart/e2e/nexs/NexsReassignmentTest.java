package com.lenskart.e2e.nexs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.*;
import com.lenskart.e2e.dataprovider.E2EDataProvider;
import com.lenskart.e2e.dataprovider.NexsE2EDataProvider;
import com.lenskart.e2e.helper.nexs.ReassignmentHelper;
import org.testng.annotations.Test;

@TestCategory(TestCategory.Category.SANITY)
public class NexsReassignmentTest {

    @Test(description = "Reassign the order to different warehouse facility and check the status in nexs",
            dataProviderClass = NexsE2EDataProvider.class, dataProvider = "nexsOrderReassignmnet")
    public void reassignTheOrder(OrderContext orderContext) {

        ReassignmentHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

}
