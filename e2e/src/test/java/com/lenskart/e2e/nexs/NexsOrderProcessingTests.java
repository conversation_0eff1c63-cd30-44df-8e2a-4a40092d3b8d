package com.lenskart.e2e.nexs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.*;
import com.lenskart.e2e.helper.nexs.NexsE2EHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.annotations.Test;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.SUNGLASSES;
import static com.lenskart.commons.model.PowerTypes.ZERO_POWER;
import static com.lenskart.commons.model.ProductId.*;

@TestCategory(TestCategory.Category.SANITY)
public class NexsOrderProcessingTests {

    private  void nexsOrderCompletion(OrderContext orderContext) {
        NexsE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(description = "Process and dispatch the Eyeglasses with power order from the warehouse")
    public void processEyeglassesWithPowerOrder() {
        OrderContext orderContext = OrderContext.builder()
                .phoneNumber("2035164014")
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode(Countries.IN.getDefaultPinCode())
                        .build())
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                .build();
        nexsOrderCompletion(orderContext);
    }

    //retryAnalyzer = Retry.class
    @Test(description = "Process and dispatch the Sunglasses with power order from the warehouse")
    public void processSunglassesWithPowerOrder(){
        OrderContext orderContext = OrderContext.builder()
                .phoneNumber("2035164014")
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .isPrescriptionRequired(true)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode(Countries.IN.getDefaultPinCode())
                        .build())
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                .build();
        nexsOrderCompletion(orderContext);
    }

    @Test(description = "Process and dispatch the Eyeglass frame only order from the warehouse")
    public void processEyeglassFrameOnlyOrder() {
        OrderContext orderContext =  OrderContext.builder()
                .phoneNumber("2035164014")
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode(Countries.IN.getDefaultPinCode())
                        .build())
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                .build();
        nexsOrderCompletion(orderContext);
    }

    @Test(description = "Process and dispatch the Sunglasses order from the warehouse")
    public void processSunglassesOrder() {
        OrderContext orderContext =  OrderContext.builder()
                .phoneNumber("2035164014")
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode(Countries.IN.getDefaultPinCode())
                        .build())
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder().client(Client.DESKTOP).build())
                .build();
        nexsOrderCompletion(orderContext);
    }

    @Test(description = "Process and dispatch the Accessories order from the warehouse")
    public void processAndDispatchAccessoriesOrderFromWarehouse() {
        OrderContext orderContext =  OrderContext.builder()
                .phoneNumber("2035164014")
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_ACCESSORIES.getProductId())
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode(Countries.IN.getDefaultPinCode())
                        .build())
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                .build();
        nexsOrderCompletion(orderContext);
    }

    @Test(description = "Process and dispatch the Contact Lens order from the warehouse")
    public void processAndDispatchContactLensOrderFromWarehouse() {
        OrderContext orderContext =  OrderContext.builder()
                .phoneNumber("2035164014")
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_CONTACT_LENS.getProductId())
                                .isPrescriptionRequired(true)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode(Countries.IN.getDefaultPinCode())
                        .build())
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                .build();
        nexsOrderCompletion(orderContext);
    }

    @Test(description = "Process and dispatch the CL w/o power from the warehouse")
    public void processAndDispatchContactLensWithoutPowerOrderFromWarehouse() {
        OrderContext orderContext =  OrderContext.builder()
                .phoneNumber("2035164014")
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_CONTACT_LENS_ZERO_POWER.getProductId())
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode(Countries.IN.getDefaultPinCode())
                        .build())
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                .build();
        nexsOrderCompletion(orderContext);
    }

    @Test
    public void processAndDispatch() {
        OrderContext orderContext =  OrderContext.builder()
                .phoneNumber("2035164014")
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .finalState(NexsOrderState.DISPATCHED)
                                .build(),

                        OrderContext.ProductList.builder()
                                .productId(IN_LOYALTY.getProductId())
                                .build()))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode(Countries.IN.getDefaultPinCode())
                        .build())
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                .build();
        nexsOrderCompletion(orderContext);
    }

}
