package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import org.testng.annotations.DataProvider;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.ZERO_POWER;
import static com.lenskart.commons.model.ProductId.IN_EYEGLASSES;

public class JunoE2EDataProvider {


    @DataProvider(name = "orderContext")
    public Object[][] orderContext() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build()

                }
        };
    }
}
