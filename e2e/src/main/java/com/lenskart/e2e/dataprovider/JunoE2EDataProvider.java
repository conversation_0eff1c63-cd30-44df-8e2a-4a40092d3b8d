package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import org.testng.annotations.DataProvider;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.*;
import static com.lenskart.commons.model.ProductId.*;

public class JunoE2EDataProvider {


    @DataProvider(name = "eyeGlassSingeVisionPower")
    public Object[][] eyeGlassSingeVisionPower() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build()

                }
        };
    }

    @DataProvider(name = "eyeGlassBifocalPower")
    public Object[][] eyeGlassBifocalPower() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(BIFOCAL)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build()

                }
        };
    }

    @DataProvider(name = "eyeGlassZeroPower")
    public Object[][] eyeGlassZeroPower() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build()

                }
        };
    }

    @DataProvider(name = "eyeglassWithoutPower")
    public Object[][] eyeglassWithoutPower() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build()

                }
        };
    }


    @DataProvider(name = "sunglassWithoutPower")
    public Object[][] sunglassWithoutPower() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES.getProductId())
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build()

                }
        };
    }

    @DataProvider(name = "sunglassWithPower")
    public Object[][] sunglassWithPower() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES.getProductId())
                                                .powerType(SUNGLASSES)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build()

                }
        };
    }

    @DataProvider(name = "goldPid")
    public Object[][] goldPid() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_LOYALTY.getProductId())
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build()

                }
        };
    }


    @DataProvider(name = "frameOnly")
    public Object[][] frameOnlyOrderCreation() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_FRAME_ONLY.getProductId())
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build()

                }
        };
    }

    @DataProvider(name = "contactLens")
    public Object[][] contactLensOrderCreation() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_CONTACT_LENS.getProductId())
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build()
                }
        };
    }

    @DataProvider(name = "contactLensZeroPower")
    public Object[][] contactLensZeroPowerOrderCreation() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_CONTACT_LENS_ZERO_POWER.getProductId())
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build()
                }
        };
    }

    @DataProvider(name = "accessories")
    public Object[][] accessoriesOrderCreation() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_ACCESSORIES.getProductId())
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build()
                }
        };

    }


    @DataProvider(name = "eyeglassesZeroPowerWithSunglassesAndContactLens")
    public Object[][] eyeglassesZeroPowerWithSunglassesAndContactLensOrderCreation() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_FRAME_ONLY.getProductId())
                                                .build(),
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES.getProductId())
                                                .build(),
                                        OrderContext.ProductList.builder()
                                                .productId(IN_CONTACT_LENS.getProductId())
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build()
                }
        };
    }

    @DataProvider(name = "eyeglassesWithPowerAndSunglassesAndContactLens")
    public Object[][] eyeglassesWithPowerAndSunglassesAndContactLensOrderCreation() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .build(),
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES.getProductId())
                                                .build(),
                                        OrderContext.ProductList.builder()
                                                .productId(IN_CONTACT_LENS.getProductId())
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build()
                }

        };
    }

}





