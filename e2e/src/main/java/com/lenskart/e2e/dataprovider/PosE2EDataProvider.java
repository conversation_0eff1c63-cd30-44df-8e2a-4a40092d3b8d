package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import com.lenskart.pos.model.POS;
import org.testng.annotations.DataProvider;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.SINGLE_VISION;
import static com.lenskart.commons.model.PowerTypes.SUNGLASSES;
import static com.lenskart.commons.model.ProductId.IN_EYEGLASSES;

public class PosE2EDataProvider {

    @DataProvider(name = "posOrderContext")
    public Object[][] posOrderContext() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId("148248")
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build(),
                                        OrderContext.ProductList.builder()
                                                .productId("131315")
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }
//                , {
//                        OrderContext.builder()
//                                .phoneNumber("**********")
//                                .productLists(List.of(
//                                        OrderContext.ProductList.builder()
//                                                .productId("148248")
//                                                .powerType(SINGLE_VISION)
//                                                .itemType(ItemType.LOCAL_FITTING)
//                                                .finalState(NexsOrderState.DISPATCHED)
//                                                .isPrescriptionRequired(true)
//                                                .build()))
//                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
//                                        .country(Countries.IN)
//                                        .pinCode("560075")
//                                        .build())
//                                .isPosOrder(true)
//                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
//                                        .country(Countries.IN)
//                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
//                                        .build())
//                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
//                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
//                                .build()
//                }, {
//                        OrderContext.builder()
//                                .phoneNumber("**********")
//                                .productLists(List.of(
//                                        OrderContext.ProductList.builder()
//                                                .productId("131315")
//                                                .powerType(SUNGLASSES)
//                                                .itemType(ItemType.B2B)
//                                                .finalState(NexsOrderState.DISPATCHED)
//                                                .isPrescriptionRequired(false)
//                                                .build()))
//                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
//                                        .country(Countries.IN)
//                                        .pinCode("560075")
//                                        .build())
//                                .isPosOrder(true)
//                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
//                                        .country(Countries.IN)
//                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
//                                        .build())
//                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
//                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
//                                .build()
//                }, {
//                OrderContext.builder()
//                        .phoneNumber("**********")
//                        .productLists(List.of(
//                                OrderContext.ProductList.builder()
//                                        .productId("148248")
//                                        .powerType(SINGLE_VISION)
//                                        .itemType(ItemType.DTC)
//                                        .finalState(NexsOrderState.DISPATCHED)
//                                        .isPrescriptionRequired(false)
//                                        .build()))
//                        .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
//                                .country(Countries.IN)
//                                .pinCode("560075")
//                                .build())
//                        .isPosOrder(true)
//                        .posStoreMapper(OrderContext.PosStoreMapper.builder()
//                                .country(Countries.IN)
//                                .storeId(POS.IN_FOFO_STORE.getStoreId())
//                                .build())
//                        .paymentMethod(PaymentMethod.OFFLINE_CASH)
//                        .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
//                        .build()
//                }, {
//                OrderContext.builder()
//                        .phoneNumber("**********")
//                        .productLists(List.of(
//                                OrderContext.ProductList.builder()
//                                        .productId("148248")
//                                        .powerType(SINGLE_VISION)
//                                        .itemType(ItemType.LAST_PIECE_WAREHOUSE)
//                                        .finalState(NexsOrderState.DISPATCHED)
//                                        .isPrescriptionRequired(true)
//                                        .build()))
//                        .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
//                                .country(Countries.IN)
//                                .pinCode("560075")
//                                .build())
//                        .isPosOrder(true)
//                        .posStoreMapper(OrderContext.PosStoreMapper.builder()
//                                .country(Countries.IN)
//                                .storeId(POS.IN_FOFO_STORE.getStoreId())
//                                .build())
//                        .paymentMethod(PaymentMethod.OFFLINE_CASH)
//                        .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
//                        .build()
//                }

        };
    }
}
