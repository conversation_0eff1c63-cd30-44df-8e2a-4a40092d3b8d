package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.pos.model.POS;
import org.testng.annotations.DataProvider;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.*;

public class E2EDataProvider {

    @DataProvider(name = "orderContext")
    public Object[][] orderContext() {
        return new Object[][]{

                {

                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId("148248")
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build()

                }
        };
    }




    @DataProvider(name = "returnContext")
    public Object[][] returnContext() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId("148248")
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("122001")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().productIDToBeReturned("148248").build()

                }
        };
    }

    @DataProvider(name = "cancellationContext")
    public Object[][] cancellationContext() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId("148248")
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("121004")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().cancellationType("full_cancellation")
                        .cancellationReason("test order")
                        .cancelledBy("<EMAIL>")
                        .cancellationSource("vsm")
                        .paymentMethod("source")
                        .cancelledOrderShipmentStatus("order_not_confirmed")
                        .cancellationReasonID(204).build()

                }
        };
    }

    @DataProvider(name = "nexsCancellationContext")
    public Object[][] nexsCancellationContext() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId("148248")
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("121004")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().cancellationType("full_cancellation")
                        .cancellationReason("test order")
                        .cancelledBy("<EMAIL>")
                        .cancellationSource("vsm")
                        .paymentMethod("source")
                        .cancellationReasonID(204).build()

                }
        };
    }

    @DataProvider(name = "posOrderContext")
    public Object[][] posOrderContext() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId("148248")
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }, {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId("148248")
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.LOCAL_FITTING)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }, {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId("131315")
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }, {
                OrderContext.builder()
                        .phoneNumber("**********")
                        .productLists(List.of(
                                OrderContext.ProductList.builder()
                                        .productId("148248")
                                        .powerType(SINGLE_VISION)
                                        .itemType(ItemType.DTC)
                                        .finalState(NexsOrderState.DISPATCHED)
                                        .isPrescriptionRequired(false)
                                        .build()))
                        .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                .country(Countries.IN)
                                .pinCode("560075")
                                .build())
                        .isPosOrder(true)
                        .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                .country(Countries.IN)
                                .storeId(POS.IN_FOFO_STORE.getStoreId())
                                .build())
                        .paymentMethod(PaymentMethod.OFFLINE_CASH)
                        .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                        .build()
                }, {
                OrderContext.builder()
                        .phoneNumber("**********")
                        .productLists(List.of(
                                OrderContext.ProductList.builder()
                                        .productId("148248")
                                        .powerType(SINGLE_VISION)
                                        .itemType(ItemType.LAST_PIECE_WAREHOUSE)
                                        .finalState(NexsOrderState.DISPATCHED)
                                        .isPrescriptionRequired(true)
                                        .build()))
                        .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                .country(Countries.IN)
                                .pinCode("560075")
                                .build())
                        .isPosOrder(true)
                        .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                .country(Countries.IN)
                                .storeId(POS.IN_FOFO_STORE.getStoreId())
                                .build())
                        .paymentMethod(PaymentMethod.OFFLINE_CASH)
                        .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                        .build()
                }

        };
    }
}

