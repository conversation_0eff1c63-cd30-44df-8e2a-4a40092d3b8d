package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.PowerTypes;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.CartResponse;
import lombok.extern.slf4j.Slf4j;
import com.lenskart.pos.model.PrescriptionData;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.Map;

import static com.lenskart.commons.constants.Constants.CART_ID;
import static com.lenskart.pos.endpoints.PosEndpoints.ADD_PRESCRIPTION_TO_CART;
import static com.lenskart.pos.endpoints.PosEndpoints.GET_DELIVERY_ESTIMATES;

@SuperBuilder
@Slf4j
@Getter
public class AddPrescriptionToCartHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    PrescriptionData prescriptionData;
    Response response;

    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (prescriptionData == null) {
            throw new RuntimeException("Prescription data is required to add prescription to cart");
        }

            // Ensure the power type is in uppercase enum format (SINGLE_VISION instead of single_vision)
            PowerTypes powerType = orderContext.getProductLists().get(0).getPowerType();
            prescriptionData.setPowerType(powerType.name());

            log.info("Adding prescription to cart with powerType: {}", prescriptionData.getPowerType());

            // Get the cart details to fetch the item ID
            GetCartHelper getCartHelper = GetCartHelper.builder()
                    .orderContext(orderContext)
                    .build();
            getCartHelper.test();

            CartResponse cartResponse = getCartHelper.getCartResponse();

            if (cartResponse == null || cartResponse.getItems() == null || cartResponse.getItems().isEmpty()) {
                throw new RuntimeException("No items found in cart");
        }

        // Get the first item ID from the cart response
            Object firstItem = cartResponse.getItems().get(0);
            Map<String, Object> itemMap = (Map<String, Object>) firstItem;
            String itemId = String.valueOf(itemMap.get("id"));

            log.info("Using item ID from cart: {}", itemId);

            // Make POST request to add prescription to cart
            String url = ADD_PRESCRIPTION_TO_CART.getUrl(Map.of("itemId", itemId)) + "?isManualPowerEntry=false";
            log.info("Request URL: {}", url);

            response = RestUtils.post(
                    url,
                    headers,
                    JsonUtils.convertObjectToJsonString(prescriptionData),
                    200
        );

        log.info("Add prescription to cart response: {}", response.asPrettyString());

        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}