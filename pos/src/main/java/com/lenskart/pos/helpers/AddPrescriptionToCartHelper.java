package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.PowerTypes;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.CartResponse;
import lombok.extern.slf4j.Slf4j;
import com.lenskart.pos.model.PrescriptionData;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.lenskart.commons.constants.Constants.CART_ID;
import static com.lenskart.pos.endpoints.PosEndpoints.ADD_PRESCRIPTION_TO_CART;
import static com.lenskart.pos.endpoints.PosEndpoints.GET_DELIVERY_ESTIMATES;

@SuperBuilder
@Slf4j
@Getter
public class AddPrescriptionToCartHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    PrescriptionData prescriptionData;
    Response response;
    List<Response> responses; // Store responses for multiple prescriptions

    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext);
        responses = new java.util.ArrayList<>();
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (prescriptionData == null) {
            throw new RuntimeException("Prescription data is required to add prescription to cart");
        }

        // Get the cart details to fetch all item IDs
        GetCartHelper getCartHelper = GetCartHelper.builder()
                .orderContext(orderContext)
                .build();
        getCartHelper.test();

        CartResponse cartResponse = getCartHelper.getCartResponse();

        if (cartResponse == null || cartResponse.getItems() == null || cartResponse.getItems().isEmpty()) {
            throw new RuntimeException("No items found in cart");
        }

        log.info("Found {} items in cart", cartResponse.getItems().size());

        // Process each product from the data provider
        for (OrderContext.ProductList productFromDataProvider : orderContext.getProductLists()) {
            // Skip products that don't require prescription
            if (!productFromDataProvider.isPrescriptionRequired()) {
                log.info("Skipping product {} - prescription not required", productFromDataProvider.getProductId());
                continue;
            }

            log.info("Processing product {} with powerType {}",
                    productFromDataProvider.getProductId(),
                    productFromDataProvider.getPowerType().name());

            // Find matching cart item by productId and powerType
            Optional<Map<String, Object>> matchingCartItem = findMatchingCartItem(
                    cartResponse.getItems(),
                    productFromDataProvider.getProductId(),
                    productFromDataProvider.getPowerType().name()
            );

            if (matchingCartItem.isPresent()) {
                addPrescriptionToItem(matchingCartItem.get(), productFromDataProvider.getPowerType());
            } else {
                log.warn("No matching cart item found for product {} with powerType {}",
                        productFromDataProvider.getProductId(),
                        productFromDataProvider.getPowerType().name());
            }
        }

        return this;
    }

    /**
     * Find matching cart item by productId and powerType
     */
    private Optional<Map<String, Object>> findMatchingCartItem(List<Object> cartItems, String productId, String powerType) {
        for (Object item : cartItems) {
            Map<String, Object> itemMap = (Map<String, Object>) item;

            // Extract productId from cart item
            String cartProductId = String.valueOf(itemMap.get("productId"));

            // Extract powerType from cart item (might be in different formats)
            String cartPowerType = extractPowerTypeFromCartItem(itemMap);

            log.debug("Comparing cart item - ProductId: {}, PowerType: {} with target - ProductId: {}, PowerType: {}",
                    cartProductId, cartPowerType, productId, powerType);

            if (productId.equals(cartProductId) && powerType.equalsIgnoreCase(cartPowerType)) {
                log.info("Found matching cart item for product {} with powerType {}", productId, powerType);
                return Optional.of(itemMap);
            }
        }
        return Optional.empty();
    }

    /**
     * Extract power type from cart item (handles different possible field names)
     */
    private String extractPowerTypeFromCartItem(Map<String, Object> itemMap) {
        // Try different possible field names for power type
        Object powerType = itemMap.get("powerType");
        if (powerType == null) {
            powerType = itemMap.get("power_type");
        }
        if (powerType == null) {
            powerType = itemMap.get("type");
        }

        return powerType != null ? String.valueOf(powerType) : "";
    }

    /**
     * Add prescription to a specific cart item
     */
    private void addPrescriptionToItem(Map<String, Object> cartItem, PowerTypes powerType) {
        String itemId = String.valueOf(cartItem.get("id"));

        // Create a copy of prescription data with the correct power type
        PrescriptionData itemPrescriptionData = PrescriptionData.builder()
                .id(prescriptionData.getId())
                .powerType(powerType.name())
                .userName(prescriptionData.getUserName())
                .recordedAt(prescriptionData.getRecordedAt())
                .left(prescriptionData.getLeft())
                .right(prescriptionData.getRight())
                .relationship(prescriptionData.getRelationship())
                .queueId(prescriptionData.getQueueId())
                .build();

        log.info("Adding prescription to cart item {} with powerType: {}", itemId, powerType.name());

        // Make POST request to add prescription to cart
        String url = ADD_PRESCRIPTION_TO_CART.getUrl(Map.of("itemId", itemId)) + "?isManualPowerEntry=false";
        log.info("Request URL: {}", url);

        Response itemResponse = RestUtils.post(
                url,
                headers,
                JsonUtils.convertObjectToJsonString(itemPrescriptionData),
                200
        );

        responses.add(itemResponse);
        log.info("Add prescription to cart response for item {}: {}", itemId, itemResponse.asPrettyString());

        // Store the last response for backward compatibility
        response = itemResponse;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}