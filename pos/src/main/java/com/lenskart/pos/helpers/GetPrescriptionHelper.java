package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.PowerTypes;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.ItemPrescriptionResponse;
import com.lenskart.pos.model.PrescriptionData;
import lombok.extern.slf4j.Slf4j;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import java.util.Optional;

import static com.lenskart.pos.endpoints.PosEndpoints.FETCH_PRESCRIPTION;

@SuperBuilder
@Slf4j
@Getter
public class GetPrescriptionHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    ItemPrescriptionResponse itemPrescriptionResponse;
    PrescriptionData selectedPrescription;

    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext);
        queryParams = getQueryParamsForPhoneNumber(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(FETCH_PRESCRIPTION.getUrl(), headers, queryParams, 200);

        String responseBody = response.asPrettyString();
        log.info("Raw prescription response: {}", responseBody);

        // Parse response with fallback mechanism
        itemPrescriptionResponse = parseResponseWithFallback(responseBody);
        log.info("Prescription data: {}", itemPrescriptionResponse);

        // Find the first prescription matching the powerType from the order context
        if (itemPrescriptionResponse != null && itemPrescriptionResponse.getData() != null && !itemPrescriptionResponse.getData().isEmpty()) {
            PowerTypes requestedPowerType = orderContext.getProductLists().get(0).getPowerType();
            log.info("Requested powerType: {}", requestedPowerType.name());

            Optional<PrescriptionData> matchingPrescription = itemPrescriptionResponse.getData().stream()
                .filter(prescription -> prescription.getPowerType().equalsIgnoreCase(requestedPowerType.name()))
                .findFirst();

            if (matchingPrescription.isPresent()) {
                selectedPrescription = matchingPrescription.get();
                log.info("Found matching prescription with powerType: {}", requestedPowerType.name());
            } else {
                log.warn("No matching prescription found for powerType: {}", requestedPowerType.name());
                
                // If no matching prescription found, use the first one if available
                selectedPrescription = itemPrescriptionResponse.getData().get(0);
                log.info("Using first available prescription with powerType: {}", selectedPrescription.getPowerType());
                
                // Update the power type to match what's expected
                selectedPrescription.setPowerType(requestedPowerType.name());
            }
        } else {
            log.error("No prescriptions found in the response");
        }
        
        return this;
    }

    @Override
    public ServiceHelper validate() {
        if (selectedPrescription == null) {
            throw new RuntimeException("No prescription found for the customer");
        }
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
