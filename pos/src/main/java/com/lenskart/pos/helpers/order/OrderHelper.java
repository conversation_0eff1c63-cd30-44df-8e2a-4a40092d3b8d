package com.lenskart.pos.helpers.order;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.helpers.ClearCartHelper;
import com.lenskart.pos.helpers.*;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

@SuperBuilder
public class OrderHelper extends PosBaseHelper implements ExecutionHelper {


    OrderContext orderContext;
    JSONObject payload;
    Response response;


    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        /* Authenticate User */
        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Search for product and get package id */
        GetProductAndPackageHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

//        /* Clear Cart from juno module */
//        ClearCartHelper.builder()
//                .orderContext(orderContext)
//                .build()
//                .test();

        /* Create Cart */
        CreateCartHelper cartHelper = CreateCartHelper.builder()
                .orderContext(orderContext)
                .build();
        cartHelper.test();

        /* Remove auto-added items from cart */
        RemoveAutoAddedItemsHelper removeAutoAddedItemsHelper = RemoveAutoAddedItemsHelper.builder()
                .orderContext(orderContext)
                .cartResponse(cartHelper.getCartResponse())
                .build();
        removeAutoAddedItemsHelper.test();

//        GetCustomerDetailsHelper.builder()
//                .orderContext(orderContext)
//                .build()
//                .test();

        /* Get prescription data */
        GetPrescriptionHelper getPrescriptionHelper = GetPrescriptionHelper.builder()
                .orderContext(orderContext)
                .build();
        getPrescriptionHelper.test();

        /* Add prescription to cart only if prescription data is available */
        if (getPrescriptionHelper.getSelectedPrescription() != null) {
            AddPrescriptionToCartHelper.builder()
                    .orderContext(orderContext)
                    .prescriptionData(getPrescriptionHelper.getSelectedPrescription())
                    .build()
                    .test();
        }

        /* Create Order Payment with updated cart */
        CreateOrderPaymentHelper.builder()
                .orderContext(orderContext)
                .cartHelper(cartHelper)
                .cartResponse(removeAutoAddedItemsHelper.getUpdatedCartResponse())
                .build()
                .test();

        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
